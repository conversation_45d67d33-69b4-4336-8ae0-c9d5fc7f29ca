"use client"

import { ProductGrid } from "@/components/product-grid"
import { OptimizedProductCarousel } from "@/components/optimized-product-carousel"
import { mockProducts } from "@/lib/mock-data"
import { Product } from "@/lib/types"

export function FeaturedProducts() {
  // Get all products (only products with real images, no placeholders)
  const allProducts = mockProducts
    .filter(product => !product.images[0]?.includes('placeholder'))

  return (
    <OptimizedProductCarousel
      products={allProducts}
      title="All Products"
      subtitle="Discover our complete collection of quality products - from electronics to fashion, beauty to food"
      className="bg-white"
      autoPlay={true}
      autoPlayInterval={4000}
      slideshowMode="fade"
      showProgress={true}
      showSlideCounter={true}
      pauseOnHover={true}
    />
  )
}

export function NewArrivals() {
  // Get newest products (only products with real images)
  const newProducts = mockProducts
    .filter(product => !product.images[0]?.includes('placeholder'))
    .slice(-8) // Get last 8 products as "new arrivals"
    .reverse() // Show newest first

  return (
    <OptimizedProductCarousel
      products={newProducts}
      title="New Arrivals"
      subtitle="Be the first to discover our latest collection of cutting-edge products"
      autoPlayInterval={6000}
      className="bg-gradient-to-br from-slate-50 to-white"
      slideshowMode="zoom"
      showProgress={true}
      showSlideCounter={true}
    />
  )
}

export function BestSellers() {
  // Get best selling products (only products with real images and high ratings)
  const bestSellers = mockProducts
    .filter(product => product.rating >= 4.0 && !product.images[0]?.includes('placeholder'))
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 8)

  return (
    <OptimizedProductCarousel
      products={bestSellers}
      title="Best Sellers"
      subtitle="Join thousands of satisfied customers who love these top-rated products"
      autoPlayInterval={5000}
      className="bg-gradient-to-br from-blue-50 to-indigo-50"
      slideshowMode="slide"
      showProgress={true}
      showSlideCounter={true}
    />
  )
}

export function SaleProducts() {
  // Get products on sale (only products with real images)
  const saleProducts = mockProducts
    .filter(product => product.salePrice && !product.images[0]?.includes('placeholder'))
    .slice(0, 8)

  if (saleProducts.length === 0) {
    return null // Don't render if no sale products
  }

  return (
    <OptimizedProductCarousel
      products={saleProducts}
      title="Special Offers"
      subtitle="Limited time deals on premium products - save big while stocks last!"
      autoPlayInterval={3500}
      className="bg-gradient-to-br from-red-50 to-orange-50"
      slideshowMode="carousel"
      showProgress={true}
      showSlideCounter={true}
      pauseOnHover={true}
    />
  )
}
