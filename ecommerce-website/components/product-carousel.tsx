"use client"

import { useState, useEffect, useRef } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { WishlistButton } from "@/components/wishlist-button"
import { useCart } from "@/components/cart-context"
import { ChevronLeft, ChevronRight, ShoppingCart, Star, Play, Pause } from "lucide-react"
import { Product } from "@/lib/types"
import { cn } from "@/lib/utils"
import { toast } from "sonner"

interface ProductCarouselProps {
  products: Product[]
  title?: string
  subtitle?: string
  autoPlay?: boolean
  autoPlayInterval?: number
  showControls?: boolean
  itemsPerView?: {
    mobile: number
    tablet: number
    desktop: number
  }
  className?: string
}

export function ProductCarousel({
  products,
  title = "Featured Products",
  subtitle = "Discover our handpicked selection of premium products",
  autoPlay = true,
  autoPlayInterval = 8000,
  showControls = true,
  itemsPerView = {
    mobile: 1,
    tablet: 2,
    desktop: 4
  },
  className = ""
}: ProductCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isPlaying, setIsPlaying] = useState(autoPlay)
  const [isHovered, setIsHovered] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [startX, setStartX] = useState(0)
  const [currentX, setCurrentX] = useState(0)
  const [translateX, setTranslateX] = useState(0)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const carouselRef = useRef<HTMLDivElement>(null)
  const trackRef = useRef<HTMLDivElement>(null)
  const { addItem } = useCart()

  // Calculate items per view based on screen size
  const getItemsPerView = () => {
    if (typeof window === 'undefined') return itemsPerView.desktop
    
    if (window.innerWidth < 768) return itemsPerView.mobile
    if (window.innerWidth < 1024) return itemsPerView.tablet
    return itemsPerView.desktop
  }

  const [currentItemsPerView, setCurrentItemsPerView] = useState(getItemsPerView())

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setCurrentItemsPerView(getItemsPerView())
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Calculate the maximum number of slides needed
  const maxSlides = Math.max(1, Math.ceil(products.length / currentItemsPerView))
  const maxIndex = maxSlides - 1

  // Auto-play functionality
  useEffect(() => {
    if (isPlaying && !isHovered && maxSlides > 1) {
      intervalRef.current = setInterval(() => {
        setCurrentIndex(prev => {
          return prev >= maxIndex ? 0 : prev + 1
        })
      }, autoPlayInterval)
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [isPlaying, isHovered, products.length, currentItemsPerView, autoPlayInterval, maxIndex])

  const goToSlide = (index: number) => {
    setCurrentIndex(Math.min(Math.max(0, index), maxIndex))
  }

  const goToPrevious = () => {
    setCurrentIndex(prev => {
      // Infinite loop: go to last slide when at first slide
      return prev <= 0 ? maxIndex : prev - 1
    })
  }

  const goToNext = () => {
    setCurrentIndex(prev => {
      // Infinite loop: go to first slide when at last slide
      return prev >= maxIndex ? 0 : prev + 1
    })
  }

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying)
  }

  const handleAddToCart = (product: Product, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    addItem(product)
    toast.success(`${product.name} added to cart!`)
  }

  // Touch/Swipe handlers
  const handleTouchStart = (e: React.TouchEvent) => {
    setIsDragging(true)
    setStartX(e.touches[0].clientX)
    setCurrentX(e.touches[0].clientX)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging) return
    setCurrentX(e.touches[0].clientX)
    const diff = currentX - startX
    setTranslateX(diff)
  }

  const handleTouchEnd = () => {
    if (!isDragging) return
    setIsDragging(false)

    const diff = currentX - startX
    const threshold = 50 // Minimum swipe distance

    if (Math.abs(diff) > threshold) {
      if (diff > 0) {
        goToPrevious()
      } else {
        goToNext()
      }
    }

    setTranslateX(0)
  }

  // Mouse drag handlers for desktop
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    setStartX(e.clientX)
    setCurrentX(e.clientX)
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return
    setCurrentX(e.clientX)
    const diff = currentX - startX
    setTranslateX(diff)
  }

  const handleMouseUp = () => {
    if (!isDragging) return
    setIsDragging(false)

    const diff = currentX - startX
    const threshold = 50

    if (Math.abs(diff) > threshold) {
      if (diff > 0) {
        goToPrevious()
      } else {
        goToNext()
      }
    }

    setTranslateX(0)
  }

  return (
    <section className={cn("py-16 bg-gradient-to-br from-slate-50 to-white", className)}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            {title}
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            {subtitle}
          </p>
        </div>

        {/* Carousel Container */}
        <div
          className="relative max-w-7xl mx-auto"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {/* Navigation Controls */}
          {showControls && maxSlides > 1 && (
            <>
              <Button
                variant="outline"
                size="icon"
                className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 backdrop-blur-sm border-slate-200 hover:bg-white shadow-lg"
                onClick={goToPrevious}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <Button
                variant="outline"
                size="icon"
                className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 backdrop-blur-sm border-slate-200 hover:bg-white shadow-lg"
                onClick={goToNext}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </>
          )}

          {/* Play/Pause Control */}
          {autoPlay && showControls && (
            <Button
              variant="outline"
              size="sm"
              className="absolute top-4 right-4 z-10 bg-white/90 backdrop-blur-sm border-slate-200 hover:bg-white"
              onClick={togglePlayPause}
            >
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
          )}

          {/* Carousel Track */}
          <div
            ref={carouselRef}
            className="overflow-hidden rounded-xl cursor-grab active:cursor-grabbing"
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
          >
            <div
              ref={trackRef}
              className={cn(
                "flex transition-transform duration-700 ease-in-out",
                isDragging && "transition-none"
              )}
              style={{
                transform: `translateX(calc(-${currentIndex * 100}% + ${translateX}px))`,
                width: `${maxSlides * 100}%`
              }}
            >
              {Array.from({ length: maxSlides }).map((_, slideIndex) => (
                <div
                  key={slideIndex}
                  className="flex-shrink-0 w-full"
                  style={{ width: `${100 / maxSlides}%` }}
                >
                  <div className={cn(
                    "grid gap-6 px-6 h-full",
                    currentItemsPerView === 1 && "grid-cols-1",
                    currentItemsPerView === 2 && "grid-cols-2",
                    currentItemsPerView === 4 && "grid-cols-1 sm:grid-cols-2 lg:grid-cols-4"
                  )}>
                    {products
                      .slice(slideIndex * currentItemsPerView, (slideIndex + 1) * currentItemsPerView)
                      .map((product) => (
                        <div
                          key={product.id}
                          className="w-full flex"
                        >
                  <Link href={`/products/${product.slug}`} className="w-full h-full">
                    <Card className="group cursor-pointer hover:shadow-2xl hover:-translate-y-1 transition-all duration-300 border-slate-200 overflow-hidden bg-white rounded-xl shadow-md h-full flex flex-col">
                      <CardContent className="p-0 flex flex-col h-full">
                        {/* Product Image */}
                        <div className="relative aspect-square overflow-hidden bg-slate-100">
                          <img
                            src={product.images[0] || "/placeholder.svg"}
                            alt={product.name}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                          
                          {/* Wishlist Button */}
                          <div className="absolute top-3 right-3">
                            <WishlistButton product={product} />
                          </div>

                          {/* Sale Badge */}
                          {product.salePrice && (
                            <Badge className="absolute top-3 left-3 bg-red-500 hover:bg-red-600">
                              Sale
                            </Badge>
                          )}

                          {/* Quick Add to Cart */}
                          <div className="absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <Button
                              size="sm"
                              className="w-full bg-slate-900 hover:bg-slate-800 text-white"
                              onClick={(e) => handleAddToCart(product, e)}
                            >
                              <ShoppingCart className="h-4 w-4 mr-2" />
                              Add to Cart
                            </Button>
                          </div>
                        </div>

                        {/* Product Info */}
                        <div className="p-6 flex-grow flex flex-col justify-between">
                          {/* Top Content */}
                          <div className="space-y-3">
                            {/* Product Name */}
                            <h3 className="font-semibold text-lg text-slate-900 line-clamp-2 group-hover:text-slate-700 transition-colors">
                              {product.name}
                            </h3>

                            {/* Short Description/Tagline */}
                            <p className="text-sm text-slate-600 line-clamp-2 leading-relaxed">
                              {product.description}
                            </p>

                            {/* Rating */}
                            <div className="flex items-center gap-1">
                              <div className="flex items-center">
                                {Array.from({ length: 5 }).map((_, i) => (
                                  <Star
                                    key={i}
                                    className={cn(
                                      "h-4 w-4",
                                      i < Math.floor(product.rating)
                                        ? "fill-yellow-400 text-yellow-400"
                                        : "text-slate-300"
                                    )}
                                  />
                                ))}
                              </div>
                              <span className="text-sm text-slate-600 ml-1">
                                ({product.rating})
                              </span>
                            </div>
                          </div>

                          {/* Bottom Content */}
                          <div className="space-y-4 mt-4">
                            {/* Price */}
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <span className="text-xl font-bold text-slate-900">
                                  ${product.salePrice || product.price}
                                </span>
                                {product.salePrice && (
                                  <span className="text-sm text-slate-500 line-through">
                                    ${product.price}
                                  </span>
                                )}
                              </div>
                              {product.salePrice && (
                                <Badge variant="destructive" className="text-xs">
                                  {Math.round(((product.price - product.salePrice) / product.price) * 100)}% OFF
                                </Badge>
                              )}
                            </div>

                            {/* Call to Action Button */}
                            <Button
                              className="w-full bg-slate-900 hover:bg-slate-800 text-white font-medium py-2.5 transition-all duration-200 hover:shadow-lg"
                              onClick={(e) => handleAddToCart(product, e)}
                            >
                              <ShoppingCart className="h-4 w-4 mr-2" />
                              Add to Cart
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                          </Card>
                        </Link>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Pagination Dots */}
          {showControls && maxSlides > 1 && (
            <div className="flex justify-center mt-8 space-x-3">
              {Array.from({ length: maxSlides }).map((_, index) => (
                <button
                  key={index}
                  className={cn(
                    "h-3 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2",
                    index === currentIndex
                      ? "bg-slate-900 w-8 shadow-md"
                      : "bg-slate-300 hover:bg-slate-400 w-3"
                  )}
                  onClick={() => goToSlide(index)}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
          )}
        </div>

        {/* Progress Bar */}
        {autoPlay && isPlaying && (
          <div className="mt-4 w-full bg-slate-200 rounded-full h-1 overflow-hidden">
            <div 
              className="h-full bg-slate-900 rounded-full transition-all duration-100 ease-linear"
              style={{
                width: `${((Date.now() % autoPlayInterval) / autoPlayInterval) * 100}%`,
                animation: `carousel-progress ${autoPlayInterval}ms linear infinite`
              }}
            />
          </div>
        )}
      </div>

      {/* CSS for progress bar animation */}
      <style jsx>{`
        @keyframes carousel-progress {
          from { width: 0%; }
          to { width: 100%; }
        }
      `}</style>
    </section>
  )
}
