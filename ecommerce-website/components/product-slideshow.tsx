"use client"

import { useState, useEffect, useRef, useMemo } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useCart } from "@/components/cart-context"
import { ChevronLeft, ChevronRight, ShoppingCart, Star, Play, Pause } from "lucide-react"
import { Product } from "@/lib/types"
import { cn } from "@/lib/utils"
import { toast } from "sonner"

interface ProductSlideshowProps {
  products: Product[]
  title?: string
  subtitle?: string
  autoPlayInterval?: number
  className?: string
}

export function ProductSlideshow({
  products,
  title = "Featured Products",
  subtitle = "Discover our handpicked selection of premium products",
  autoPlayInterval = 6000,
  className = ""
}: ProductSlideshowProps) {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isPlaying, setIsPlaying] = useState(true)
  const [isHovered, setIsHovered] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const { addItem } = useCart()

  // Calculate slides based on screen size
  const getItemsPerSlide = () => {
    if (typeof window !== 'undefined') {
      if (window.innerWidth >= 1024) return 4 // Desktop
      if (window.innerWidth >= 768) return 2   // Tablet
      return 1 // Mobile
    }
    return 4
  }

  const [itemsPerSlide, setItemsPerSlide] = useState(getItemsPerSlide())

  // Calculate total slides based on current itemsPerSlide
  const totalSlides = useMemo(() => {
    return Math.ceil(products.length / itemsPerSlide)
  }, [products.length, itemsPerSlide])

  // Handle window resize and initial setup
  useEffect(() => {
    const handleResize = () => {
      const newItemsPerSlide = getItemsPerSlide()
      setItemsPerSlide(newItemsPerSlide)
      setCurrentSlide(0) // Reset to first slide on resize
    }

    // Set initial value on mount
    handleResize()

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Auto-play functionality
  useEffect(() => {
    if (isPlaying && !isHovered && totalSlides > 1) {
      intervalRef.current = setInterval(() => {
        setCurrentSlide(prev => (prev + 1) % totalSlides)
      }, autoPlayInterval)
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [isPlaying, isHovered, totalSlides, autoPlayInterval])

  const goToSlide = (slideIndex: number) => {
    setCurrentSlide(slideIndex)
  }

  const goToPrevious = () => {
    setCurrentSlide(prev => (prev - 1 + totalSlides) % totalSlides)
  }

  const goToNext = () => {
    setCurrentSlide(prev => (prev + 1) % totalSlides)
  }

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying)
  }

  const handleAddToCart = (product: Product, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    addItem(product)
    toast.success(`${product.name} added to cart!`)
  }

  // Get products for current slide
  const getCurrentSlideProducts = () => {
    const startIndex = currentSlide * itemsPerSlide
    return products.slice(startIndex, startIndex + itemsPerSlide)
  }

  if (products.length === 0) {
    return null
  }

  return (
    <section className={cn("py-16 bg-gradient-to-br from-slate-50 to-white", className)}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            {title}
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            {subtitle}
          </p>
        </div>

        {/* Slideshow Container */}
        <div 
          className="relative max-w-7xl mx-auto"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {/* Navigation Arrows */}
          {totalSlides > 1 && (
            <>
              <Button
                variant="outline"
                size="icon"
                className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 backdrop-blur-sm border-slate-200 hover:bg-white shadow-lg"
                onClick={goToPrevious}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <Button
                variant="outline"
                size="icon"
                className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 backdrop-blur-sm border-slate-200 hover:bg-white shadow-lg"
                onClick={goToNext}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </>
          )}

          {/* Play/Pause Control */}
          <Button
            variant="outline"
            size="icon"
            className="absolute top-4 right-4 z-10 bg-white/90 backdrop-blur-sm border-slate-200 hover:bg-white shadow-lg"
            onClick={togglePlayPause}
          >
            {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
          </Button>

          {/* Products Grid */}
          <div className="overflow-hidden rounded-xl">
            <div 
              className="flex transition-transform duration-700 ease-in-out"
              style={{
                transform: `translateX(-${currentSlide * 100}%)`,
                width: `${totalSlides * 100}%`
              }}
            >
              {Array.from({ length: totalSlides }).map((_, slideIndex) => {
                const slideProducts = products.slice(
                  slideIndex * itemsPerSlide, 
                  (slideIndex + 1) * itemsPerSlide
                )
                
                return (
                  <div
                    key={slideIndex}
                    className="w-full flex-shrink-0"
                    style={{ width: `${100 / totalSlides}%` }}
                  >
                    <div className={cn(
                      "grid gap-6 px-6",
                      itemsPerSlide === 1 && "grid-cols-1",
                      itemsPerSlide === 2 && "grid-cols-2",
                      itemsPerSlide === 4 && "grid-cols-4"
                    )}>
                      {slideProducts.map((product) => (
                        <Link key={product.id} href={`/products/${product.slug}`} className="block">
                          <Card className="group cursor-pointer hover:shadow-2xl hover:-translate-y-1 transition-all duration-300 border-slate-200 overflow-hidden bg-white rounded-xl shadow-md h-full">
                            <CardContent className="p-0 flex flex-col h-full">
                              {/* Product Image */}
                              <div className="relative aspect-square overflow-hidden bg-slate-100">
                                <img
                                  src={product.images[0] || "/placeholder.svg"}
                                  alt={product.name}
                                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                                />
                                
                                {/* Sale Badge */}
                                {product.salePrice && (
                                  <Badge className="absolute top-3 left-3 bg-red-500 hover:bg-red-600">
                                    Sale
                                  </Badge>
                                )}

                                {/* Quick Add to Cart */}
                                <div className="absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                  <Button
                                    size="sm"
                                    className="w-full bg-slate-900 hover:bg-slate-800 text-white"
                                    onClick={(e) => handleAddToCart(product, e)}
                                  >
                                    <ShoppingCart className="h-4 w-4 mr-2" />
                                    Quick Add
                                  </Button>
                                </div>
                              </div>

                              {/* Product Info */}
                              <div className="p-6 flex-grow flex flex-col justify-between">
                                <div className="space-y-3">
                                  {/* Product Name */}
                                  <h3 className="font-semibold text-lg text-slate-900 line-clamp-2 group-hover:text-slate-700 transition-colors">
                                    {product.name}
                                  </h3>
                                  
                                  {/* Rating */}
                                  <div className="flex items-center gap-1">
                                    <div className="flex items-center">
                                      {Array.from({ length: 5 }).map((_, i) => (
                                        <Star
                                          key={i}
                                          className={cn(
                                            "h-4 w-4",
                                            i < Math.floor(product.rating)
                                              ? "fill-yellow-400 text-yellow-400"
                                              : "text-slate-300"
                                          )}
                                        />
                                      ))}
                                    </div>
                                    <span className="text-sm text-slate-600 ml-1">
                                      ({product.rating})
                                    </span>
                                  </div>
                                </div>

                                {/* Bottom Content */}
                                <div className="space-y-4 mt-4">
                                  {/* Price */}
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                      <span className="text-xl font-bold text-slate-900">
                                        ${product.salePrice || product.price}
                                      </span>
                                      {product.salePrice && (
                                        <span className="text-sm text-slate-500 line-through">
                                          ${product.price}
                                        </span>
                                      )}
                                    </div>
                                    {product.salePrice && (
                                      <Badge variant="destructive" className="text-xs">
                                        {Math.round(((product.price - product.salePrice) / product.price) * 100)}% OFF
                                      </Badge>
                                    )}
                                  </div>
                                  
                                  {/* Call to Action Button */}
                                  <Button
                                    className="w-full bg-slate-900 hover:bg-slate-800 text-white font-medium py-2.5 transition-all duration-200 hover:shadow-lg"
                                    onClick={(e) => handleAddToCart(product, e)}
                                  >
                                    <ShoppingCart className="h-4 w-4 mr-2" />
                                    Add to Cart
                                  </Button>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        </Link>
                      ))}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Pagination Dots */}
          {totalSlides > 1 && (
            <div className="flex justify-center mt-8 space-x-3">
              {Array.from({ length: totalSlides }).map((_, index) => (
                <button
                  key={index}
                  className={cn(
                    "h-3 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2",
                    index === currentSlide
                      ? "bg-slate-900 w-8 shadow-md"
                      : "bg-slate-300 hover:bg-slate-400 w-3"
                  )}
                  onClick={() => goToSlide(index)}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
          )}

          {/* Progress Bar */}
          {isPlaying && totalSlides > 1 && (
            <div className="mt-6 w-full bg-slate-200 rounded-full h-1 overflow-hidden">
              <div 
                className="h-full bg-slate-900 rounded-full transition-all duration-100 ease-linear"
                style={{
                  animation: `slideProgress ${autoPlayInterval}ms linear infinite`
                }}
              />
            </div>
          )}
        </div>
      </div>

      {/* CSS Animation */}
      <style jsx>{`
        @keyframes slideProgress {
          from { width: 0%; }
          to { width: 100%; }
        }
      `}</style>
    </section>
  )
}
