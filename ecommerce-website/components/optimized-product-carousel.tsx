"use client"

import { useState, useEffect, use<PERSON>allback, useMemo, useRef } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { WishlistButton } from "@/components/wishlist-button"
import { useCart } from "@/components/cart-context"
import { 
  Carousel, 
  CarouselContent, 
  CarouselItem, 
  CarouselNext, 
  CarouselPrevious,
  type CarouselApi
} from "@/components/ui/carousel"
import { ShoppingCart, Star, Play, Pause } from "lucide-react"
import { Product } from "@/lib/types"
import { cn } from "@/lib/utils"
import { toast } from "sonner"
import Autoplay from "embla-carousel-autoplay"

// Performance monitoring in development
const isDev = process.env.NODE_ENV === 'development'

interface OptimizedProductCarouselProps {
  products: Product[]
  title?: string
  subtitle?: string
  autoPlay?: boolean
  autoPlayInterval?: number
  className?: string
  showDots?: boolean
  showProgress?: boolean
  slideshowMode?: 'carousel' | 'fade' | 'slide' | 'zoom'
  showSlideCounter?: boolean
  pauseOnHover?: boolean
  itemsPerView?: {
    mobile: number
    tablet: number
    desktop: number
  }
}

export function OptimizedProductCarousel({
  products,
  title = "Featured Products",
  subtitle = "Discover our handpicked selection of premium products",
  autoPlay = true,
  autoPlayInterval = 5000,
  className = "",
  showDots = true,
  showProgress = true,
  slideshowMode = 'carousel',
  showSlideCounter = true,
  pauseOnHover = true,
  itemsPerView = {
    mobile: 1,
    tablet: 2,
    desktop: 4
  }
}: OptimizedProductCarouselProps) {
  const [api, setApi] = useState<CarouselApi>()
  const [current, setCurrent] = useState(0)
  const [count, setCount] = useState(0)
  const [isPlaying, setIsPlaying] = useState(autoPlay)
  const [isHovered, setIsHovered] = useState(false)
  const [isVisible, setIsVisible] = useState(false)
  const [progress, setProgress] = useState(0)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const carouselRef = useRef<HTMLDivElement>(null)
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const { addItem } = useCart()

  // Memoized autoplay plugin with optimized settings for smooth performance
  const autoplayPlugin = useMemo(() => {
    return Autoplay({
      delay: autoPlayInterval,
      stopOnInteraction: true,
      stopOnMouseEnter: true,
      stopOnFocusIn: true,
      playOnInit: autoPlay,
      rootNode: (emblaRoot) => emblaRoot.parentElement,
    })
  }, [autoPlayInterval, autoPlay])

  // Throttled resize handler to prevent performance issues
  useEffect(() => {
    let resizeTimeout: NodeJS.Timeout

    const handleResize = () => {
      clearTimeout(resizeTimeout)
      resizeTimeout = setTimeout(() => {
        if (api) {
          api.reInit()
        }
      }, 150) // Debounce resize events
    }

    window.addEventListener('resize', handleResize, { passive: true })
    return () => {
      window.removeEventListener('resize', handleResize)
      clearTimeout(resizeTimeout)
    }
  }, [api])

  // Optimized Intersection Observer for performance
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        // Use requestAnimationFrame for smooth state updates
        requestAnimationFrame(() => {
          setIsVisible(entry.isIntersecting)
        })
      },
      {
        threshold: 0.1,
        rootMargin: '50px 0px', // Start loading slightly before visible
      }
    )

    if (carouselRef.current) {
      observer.observe(carouselRef.current)
    }

    return () => observer.disconnect()
  }, [])

  // Initialize carousel state with performance monitoring
  useEffect(() => {
    if (!api) return

    const startTime = isDev ? performance.now() : 0

    setCount(api.scrollSnapList().length)
    setCurrent(api.selectedScrollSnap() + 1)

    const handleSelect = () => {
      setIsTransitioning(true)
      requestAnimationFrame(() => {
        setCurrent(api.selectedScrollSnap() + 1)
        // Reset transition state after animation completes
        setTimeout(() => setIsTransitioning(false), 300)
      })
    }

    const handleSettle = () => {
      setIsTransitioning(false)
    }

    api.on("select", handleSelect)
    api.on("settle", handleSettle)

    if (isDev) {
      const initTime = performance.now() - startTime
      console.log(`Carousel initialized in ${initTime.toFixed(2)}ms`)
    }

    return () => {
      api.off("select", handleSelect)
      api.off("settle", handleSettle)
    }
  }, [api])

  // Progress bar animation
  useEffect(() => {
    if (!isPlaying || isHovered || !isVisible || !showProgress) {
      setProgress(0)
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current)
      }
      return
    }

    setProgress(0)
    const startTime = Date.now()

    progressIntervalRef.current = setInterval(() => {
      const elapsed = Date.now() - startTime
      const newProgress = (elapsed / autoPlayInterval) * 100

      if (newProgress >= 100) {
        setProgress(100)
        clearInterval(progressIntervalRef.current!)
      } else {
        setProgress(newProgress)
      }
    }, 50) // Update every 50ms for smooth animation

    return () => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current)
      }
    }
  }, [current, isPlaying, isHovered, isVisible, autoPlayInterval, showProgress])

  // Optimized play/pause functionality with requestAnimationFrame
  useEffect(() => {
    if (!api || !autoplayPlugin) return

    const updateAutoplay = () => {
      requestAnimationFrame(() => {
        const shouldPlay = isPlaying && (!pauseOnHover || !isHovered) && isVisible
        if (shouldPlay) {
          autoplayPlugin.play()
        } else {
          autoplayPlugin.stop()
        }
      })
    }

    updateAutoplay()
  }, [api, autoplayPlugin, isPlaying, isHovered, isVisible, pauseOnHover])

  const handleAddToCart = useCallback((product: Product, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    addItem({
      id: product.id,
      name: product.name,
      price: product.salePrice || product.price,
      image: product.images[0] || "/placeholder.svg",
      quantity: 1
    })
    
    toast.success(`${product.name} added to cart!`)
  }, [addItem])

  const togglePlayPause = useCallback(() => {
    setIsPlaying(!isPlaying)
  }, [isPlaying])

  // Optimized image preloading to prevent hanging
  useEffect(() => {
    if (!api || products.length === 0) return

    const preloadImages = () => {
      // Use requestIdleCallback for non-blocking preloading
      const preloadTask = () => {
        const currentSlideIndex = api.selectedScrollSnap()
        const itemsPerSlide = Math.floor(api.containerNode().clientWidth / 300)
        const nextSlideStart = (currentSlideIndex + 1) * itemsPerSlide

        // Preload next 3 images (reduced from 4 to prevent hanging)
        for (let i = nextSlideStart; i < Math.min(nextSlideStart + 3, products.length); i++) {
          const product = products[i]
          if (product?.images[0] && !product.images[0].includes('placeholder')) {
            const img = new Image()
            img.loading = 'lazy'
            img.decoding = 'async'
            img.src = product.images[0]
          }
        }
      }

      // Use requestIdleCallback if available, otherwise setTimeout
      if ('requestIdleCallback' in window) {
        requestIdleCallback(preloadTask, { timeout: 100 })
      } else {
        setTimeout(preloadTask, 0)
      }
    }

    api.on("select", preloadImages)
    preloadImages() // Initial preload

    return () => {
      api.off("select", preloadImages)
    }
  }, [api, products])

  // Memoized product cards with slideshow effects
  const productCards = useMemo(() => {
    return products.map((product, index) => {
      const isCurrentSlide = Math.floor(index / (itemsPerView?.desktop || 4)) === current - 1
      const slideEffectClass = slideshowMode === 'fade' ? 'slideshow-fade' :
                              slideshowMode === 'zoom' ? 'slideshow-zoom' :
                              slideshowMode === 'slide' ? 'slideshow-slide' : ''

      return (
        <CarouselItem
          key={product.id}
          className={cn(
            "pl-4 md:basis-1/2 lg:basis-1/4 optimized-carousel-item",
            slideEffectClass,
            isCurrentSlide && isTransitioning && "slide-active",
            slideshowMode !== 'carousel' && "slideshow-enhanced"
          )}
        >
        <Link href={`/products/${product.slug}`} className="block h-full">
          <Card className="group cursor-pointer hover-lift border-slate-200 overflow-hidden bg-white rounded-xl shadow-md h-full reduce-paint">
            <CardContent className="p-0 flex flex-col h-full">
              {/* Product Image */}
              <div className="relative aspect-square overflow-hidden bg-slate-100">
                <img
                  src={product.images[0] || "/placeholder.svg"}
                  alt={`${product.name} - Premium ${product.tags.join(', ')}`}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300 optimized-carousel-image"
                  loading="lazy"
                  decoding="async"
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 25vw"
                  fetchPriority="low"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement
                    const currentSrc = target.src

                    // Try fallback images in order
                    if (product.images.length > 1 && !currentSrc.includes('placeholder.svg')) {
                      const currentIndex = product.images.findIndex(img => currentSrc.includes(img))
                      const nextIndex = currentIndex + 1

                      if (nextIndex < product.images.length) {
                        target.src = product.images[nextIndex]
                        return
                      }
                    }

                    // Final fallback to placeholder
                    if (!currentSrc.includes('placeholder.svg')) {
                      target.src = "/placeholder.svg"
                    }
                  }}
                />
                
                {/* Sale Badge */}
                {product.salePrice && product.salePrice < product.price && (
                  <Badge className="absolute top-3 left-3 bg-red-500 hover:bg-red-600 text-white">
                    Sale
                  </Badge>
                )}

                {/* Wishlist Button */}
                <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
                  <WishlistButton product={product} />
                </div>

                {/* Quick Add to Cart */}
                <div className="absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    size="sm"
                    className="w-full bg-slate-900 hover:bg-slate-800 text-white"
                    onClick={(e) => handleAddToCart(product, e)}
                  >
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    Add to Cart
                  </Button>
                </div>
              </div>

              {/* Product Info */}
              <div className="p-4 flex-1 flex flex-col">
                <h3 className="font-semibold text-slate-900 mb-2 line-clamp-2 group-hover:text-slate-700 transition-colors optimized-text">
                  {product.name}
                </h3>
                
                {/* Rating */}
                <div className="flex items-center gap-1 mb-2">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="text-sm text-slate-600">{product.rating}</span>
                </div>

                {/* Price */}
                <div className="flex items-center gap-2 mt-auto">
                  <span className="text-lg font-bold text-slate-900">
                    ${(product.salePrice || product.price).toFixed(2)}
                  </span>
                  {product.salePrice && product.salePrice < product.price && (
                    <span className="text-sm text-slate-500 line-through">
                      ${product.price.toFixed(2)}
                    </span>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>
      </CarouselItem>
    ))
  }, [products, handleAddToCart])

  if (products.length === 0) {
    return null
  }

  return (
    <section className={cn("py-16", className)}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-4 mb-4">
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-900">
              {title}
            </h2>
            {showSlideCounter && count > 1 && (
              <div className="flex items-center gap-2 text-sm text-slate-500 bg-slate-100 px-3 py-1 rounded-full">
                <span className="font-medium">{current}</span>
                <span>/</span>
                <span>{count}</span>
              </div>
            )}
          </div>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            {subtitle}
          </p>

          {/* Progress Bar */}
          {showProgress && autoPlay && (
            <div className="mt-6 max-w-md mx-auto">
              <div className="w-full bg-slate-200 rounded-full h-1 overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full transition-all duration-100 ease-linear"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          )}
        </div>

        {/* Carousel Container */}
        <div
          ref={carouselRef}
          className="relative max-w-7xl mx-auto"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {/* Enhanced Play/Pause Control */}
          {autoPlay && (
            <div className="absolute top-4 right-4 z-10 flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                className={cn(
                  "bg-white/90 backdrop-blur-sm border-slate-200 hover:bg-white shadow-lg transition-all duration-300",
                  isPlaying ? "text-blue-600 border-blue-200" : "text-slate-600"
                )}
                onClick={togglePlayPause}
              >
                {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </Button>

              {/* Slideshow Mode Indicator */}
              <div className="bg-white/90 backdrop-blur-sm border border-slate-200 rounded-md px-2 py-1 text-xs text-slate-600 shadow-lg">
                {slideshowMode.charAt(0).toUpperCase() + slideshowMode.slice(1)}
              </div>
            </div>
          )}

          <Carousel
            setApi={setApi}
            className="w-full optimized-carousel"
            plugins={autoPlay ? [autoplayPlugin] : []}
            opts={{
              align: "start",
              loop: true,
              skipSnaps: false,
              dragFree: false,
              containScroll: "trimSnaps",
              watchDrag: true,
              watchResize: true,
              watchSlides: true,
              duration: 25, // Smooth transition duration
              startIndex: 0,
              inViewThreshold: 0.7,
            }}
          >
            <CarouselContent className="-ml-4">
              {productCards}
            </CarouselContent>
            
            <CarouselPrevious className="left-4 bg-white/90 backdrop-blur-sm border-slate-200 hover:bg-white shadow-lg" />
            <CarouselNext className="right-4 bg-white/90 backdrop-blur-sm border-slate-200 hover:bg-white shadow-lg" />
          </Carousel>

          {/* Enhanced Dots Indicator */}
          {showDots && count > 1 && (
            <div className="flex justify-center items-center gap-3 mt-8">
              {Array.from({ length: count }).map((_, index) => {
                const isActive = current === index + 1
                return (
                  <button
                    key={index}
                    className={cn(
                      "relative transition-all duration-500 ease-out rounded-full",
                      "hover:scale-110 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
                      isActive
                        ? "w-8 h-2 bg-gradient-to-r from-blue-500 to-purple-600"
                        : "w-2 h-2 bg-slate-300 hover:bg-slate-400"
                    )}
                    onClick={() => api?.scrollTo(index)}
                    aria-label={`Go to slide ${index + 1}`}
                  >
                    {isActive && (
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full animate-pulse opacity-50" />
                    )}
                  </button>
                )
              })}
            </div>
          )}
        </div>
      </div>
    </section>
  )
}
