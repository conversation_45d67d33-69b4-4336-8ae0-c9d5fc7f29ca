"use client"

import { useState, useEffect, use<PERSON><PERSON>back, useMemo, useRef } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { WishlistButton } from "@/components/wishlist-button"
import { useCart } from "@/components/cart-context"
import { 
  Carousel, 
  CarouselContent, 
  CarouselItem, 
  CarouselNext, 
  CarouselPrevious,
  type CarouselApi
} from "@/components/ui/carousel"
import { ShoppingCart, Star, Play, Pause } from "lucide-react"
import { Product } from "@/lib/types"
import { cn } from "@/lib/utils"
import { toast } from "sonner"
import Autoplay from "embla-carousel-autoplay"

interface OptimizedProductCarouselProps {
  products: Product[]
  title?: string
  subtitle?: string
  autoPlay?: boolean
  autoPlayInterval?: number
  className?: string
  showDots?: boolean
  itemsPerView?: {
    mobile: number
    tablet: number
    desktop: number
  }
}

export function OptimizedProductCarousel({
  products,
  title = "Featured Products",
  subtitle = "Discover our handpicked selection of premium products",
  autoPlay = true,
  autoPlayInterval = 5000,
  className = "",
  showDots = true,
  itemsPerView = {
    mobile: 1,
    tablet: 2,
    desktop: 4
  }
}: OptimizedProductCarouselProps) {
  const [api, setApi] = useState<CarouselApi>()
  const [current, setCurrent] = useState(0)
  const [count, setCount] = useState(0)
  const [isPlaying, setIsPlaying] = useState(autoPlay)
  const [isHovered, setIsHovered] = useState(false)
  const [isVisible, setIsVisible] = useState(false)
  const carouselRef = useRef<HTMLDivElement>(null)
  const { addItem } = useCart()

  // Memoized autoplay plugin to prevent recreation on every render
  const autoplayPlugin = useMemo(() => {
    return Autoplay({
      delay: autoPlayInterval,
      stopOnInteraction: true,
      stopOnMouseEnter: true,
      playOnInit: autoPlay
    })
  }, [autoPlayInterval, autoPlay])

  // Intersection Observer for performance optimization
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting)
      },
      { threshold: 0.1 }
    )

    if (carouselRef.current) {
      observer.observe(carouselRef.current)
    }

    return () => observer.disconnect()
  }, [])

  // Initialize carousel state
  useEffect(() => {
    if (!api) return

    setCount(api.scrollSnapList().length)
    setCurrent(api.selectedScrollSnap() + 1)

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1)
    })
  }, [api])

  // Handle play/pause functionality
  useEffect(() => {
    if (!api || !autoplayPlugin) return

    if (isPlaying && !isHovered && isVisible) {
      autoplayPlugin.play()
    } else {
      autoplayPlugin.stop()
    }
  }, [api, autoplayPlugin, isPlaying, isHovered, isVisible])

  const handleAddToCart = useCallback((product: Product, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    addItem({
      id: product.id,
      name: product.name,
      price: product.salePrice || product.price,
      image: product.images[0] || "/placeholder.svg",
      quantity: 1
    })
    
    toast.success(`${product.name} added to cart!`)
  }, [addItem])

  const togglePlayPause = useCallback(() => {
    setIsPlaying(!isPlaying)
  }, [isPlaying])

  // Preload next few images for better performance
  useEffect(() => {
    if (!api || products.length === 0) return

    const preloadImages = () => {
      const currentSlideIndex = api.selectedScrollSnap()
      const itemsPerSlide = Math.floor(api.containerNode().clientWidth / 300) // Approximate items per slide
      const nextSlideStart = (currentSlideIndex + 1) * itemsPerSlide

      // Preload next 4 images
      for (let i = nextSlideStart; i < Math.min(nextSlideStart + 4, products.length); i++) {
        const product = products[i]
        if (product?.images[0] && !product.images[0].includes('placeholder')) {
          const img = new Image()
          img.src = product.images[0]
        }
      }
    }

    api.on("select", preloadImages)
    preloadImages() // Initial preload

    return () => {
      api.off("select", preloadImages)
    }
  }, [api, products])

  // Memoized product cards to prevent unnecessary re-renders
  const productCards = useMemo(() => {
    return products.map((product) => (
      <CarouselItem
        key={product.id}
        className="pl-4 md:basis-1/2 lg:basis-1/4 optimized-carousel-item"
      >
        <Link href={`/products/${product.slug}`} className="block h-full">
          <Card className="group cursor-pointer hover-lift border-slate-200 overflow-hidden bg-white rounded-xl shadow-md h-full reduce-paint">
            <CardContent className="p-0 flex flex-col h-full">
              {/* Product Image */}
              <div className="relative aspect-square overflow-hidden bg-slate-100">
                <img
                  src={product.images[0] || "/placeholder.svg"}
                  alt={product.name}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300 optimized-carousel-image"
                  loading="lazy"
                  decoding="async"
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 25vw"
                />
                
                {/* Sale Badge */}
                {product.salePrice && product.salePrice < product.price && (
                  <Badge className="absolute top-3 left-3 bg-red-500 hover:bg-red-600 text-white">
                    Sale
                  </Badge>
                )}

                {/* Wishlist Button */}
                <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
                  <WishlistButton product={product} />
                </div>

                {/* Quick Add to Cart */}
                <div className="absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    size="sm"
                    className="w-full bg-slate-900 hover:bg-slate-800 text-white"
                    onClick={(e) => handleAddToCart(product, e)}
                  >
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    Add to Cart
                  </Button>
                </div>
              </div>

              {/* Product Info */}
              <div className="p-4 flex-1 flex flex-col">
                <h3 className="font-semibold text-slate-900 mb-2 line-clamp-2 group-hover:text-slate-700 transition-colors optimized-text">
                  {product.name}
                </h3>
                
                {/* Rating */}
                <div className="flex items-center gap-1 mb-2">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="text-sm text-slate-600">{product.rating}</span>
                </div>

                {/* Price */}
                <div className="flex items-center gap-2 mt-auto">
                  <span className="text-lg font-bold text-slate-900">
                    ${(product.salePrice || product.price).toFixed(2)}
                  </span>
                  {product.salePrice && product.salePrice < product.price && (
                    <span className="text-sm text-slate-500 line-through">
                      ${product.price.toFixed(2)}
                    </span>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>
      </CarouselItem>
    ))
  }, [products, handleAddToCart])

  if (products.length === 0) {
    return null
  }

  return (
    <section className={cn("py-16", className)}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            {title}
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            {subtitle}
          </p>
        </div>

        {/* Carousel Container */}
        <div
          ref={carouselRef}
          className="relative max-w-7xl mx-auto"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {/* Play/Pause Control */}
          {autoPlay && (
            <Button
              variant="outline"
              size="icon"
              className="absolute top-4 right-4 z-10 bg-white/90 backdrop-blur-sm border-slate-200 hover:bg-white shadow-lg"
              onClick={togglePlayPause}
            >
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
          )}

          <Carousel
            setApi={setApi}
            className="w-full optimized-carousel"
            plugins={autoPlay ? [autoplayPlugin] : []}
            opts={{
              align: "start",
              loop: true,
              skipSnaps: false,
              dragFree: false,
              containScroll: "trimSnaps",
              watchDrag: true,
            }}
          >
            <CarouselContent className="-ml-4">
              {productCards}
            </CarouselContent>
            
            <CarouselPrevious className="left-4 bg-white/90 backdrop-blur-sm border-slate-200 hover:bg-white shadow-lg" />
            <CarouselNext className="right-4 bg-white/90 backdrop-blur-sm border-slate-200 hover:bg-white shadow-lg" />
          </Carousel>

          {/* Dots Indicator */}
          {showDots && count > 1 && (
            <div className="flex justify-center gap-2 mt-8">
              {Array.from({ length: count }).map((_, index) => (
                <button
                  key={index}
                  className={cn(
                    "w-2 h-2 rounded-full transition-all duration-300",
                    current === index + 1 
                      ? "bg-slate-900 w-8" 
                      : "bg-slate-300 hover:bg-slate-400"
                  )}
                  onClick={() => api?.scrollTo(index)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </section>
  )
}
