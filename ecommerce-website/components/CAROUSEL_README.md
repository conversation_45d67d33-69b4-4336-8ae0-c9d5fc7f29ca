# Optimized Product Carousel

A high-performance, smooth-scrolling product carousel component built with React, TypeScript, and Embla Carousel for optimal user experience and performance.

## Features

### 🚀 Performance Optimizations

- **Hardware Acceleration**: Uses CSS transforms and `will-change` properties for smooth animations
- **Intersection Observer**: Only starts autoplay when carousel is visible in viewport
- **Lazy Loading**: Images load only when needed with `loading="lazy"` and `decoding="async"`
- **Memoized Components**: Product cards are memoized to prevent unnecessary re-renders
- **Virtual Scrolling**: Optional virtual scrolling for large product lists (via `use-virtual-carousel` hook)
- **Performance Monitoring**: Built-in performance metrics tracking (via `use-carousel-performance` hook)

### 🎨 Smooth Animations

- **Embla Carousel**: Uses the modern Embla carousel library for buttery-smooth scrolling
- **Custom CSS Classes**: Optimized CSS classes for better rendering performance
- **Responsive Design**: Adapts to different screen sizes (mobile: 1 item, tablet: 2 items, desktop: 4 items)
- **Smooth Transitions**: CSS cubic-bezier transitions for natural movement

### 🎛️ User Controls

- **Auto-play**: Configurable auto-play with pause on hover
- **Navigation Arrows**: Previous/next buttons with smooth transitions
- **Dot Indicators**: Visual indicators showing current slide position
- **Play/Pause Button**: Manual control over auto-play functionality
- **Touch/Swipe Support**: Native touch gestures on mobile devices

### 🛒 E-commerce Features

- **Add to Cart**: Quick add-to-cart functionality with toast notifications
- **Wishlist Integration**: Heart icon for adding products to wishlist
- **Product Links**: Clickable product cards linking to detail pages
- **Sale Badges**: Visual indicators for products on sale
- **Star Ratings**: Product rating display
- **Price Display**: Regular and sale price formatting

## Usage

### Basic Implementation

```tsx
import { OptimizedProductCarousel } from "@/components/optimized-product-carousel"

export function ProductSection() {
  return (
    <OptimizedProductCarousel
      products={products}
      title="Featured Products"
      subtitle="Discover our handpicked selection"
      autoPlay={true}
      autoPlayInterval={5000}
      className="bg-white"
    />
  )
}
```

### Advanced Configuration

```tsx
<OptimizedProductCarousel
  products={products}
  title="All Products"
  subtitle="Complete collection"
  autoPlay={true}
  autoPlayInterval={4000}
  showDots={true}
  itemsPerView={{
    mobile: 1,
    tablet: 2,
    desktop: 4
  }}
  className="bg-gradient-to-br from-slate-50 to-white"
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `products` | `Product[]` | Required | Array of product objects |
| `title` | `string` | "Featured Products" | Carousel section title |
| `subtitle` | `string` | "Discover our handpicked selection..." | Carousel section subtitle |
| `autoPlay` | `boolean` | `true` | Enable/disable auto-play |
| `autoPlayInterval` | `number` | `5000` | Auto-play interval in milliseconds |
| `className` | `string` | `""` | Additional CSS classes |
| `showDots` | `boolean` | `true` | Show/hide dot indicators |
| `itemsPerView` | `object` | `{mobile: 1, tablet: 2, desktop: 4}` | Items per view by screen size |

## Performance Features

### CSS Optimizations

The carousel includes custom CSS classes for optimal performance:

- `.optimized-carousel`: Hardware acceleration for the main container
- `.optimized-carousel-item`: Optimized rendering for individual items
- `.optimized-carousel-image`: Optimized image rendering
- `.hover-lift`: Performance-optimized hover effects

### Intersection Observer

The carousel only starts auto-playing when it's visible in the viewport, saving resources when the user isn't viewing it.

### Memory Management

- Automatic cleanup of event listeners and intervals
- Memoized components to prevent unnecessary re-renders
- Optimized image loading with lazy loading attributes

## Browser Support

- Modern browsers with ES6+ support
- Mobile browsers with touch gesture support
- Browsers supporting Intersection Observer API
- Hardware acceleration support recommended

## Dependencies

- `embla-carousel-react`: Modern carousel library
- `embla-carousel-autoplay`: Auto-play plugin
- `lucide-react`: Icons
- `tailwindcss`: Styling
- `sonner`: Toast notifications

## Performance Monitoring

Use the included performance hooks for monitoring:

```tsx
import { useCarouselPerformance } from "@/hooks/use-carousel-performance"

const metrics = useCarouselPerformance(true) // Enable in development
console.log(`FPS: ${metrics.fps}, Render Time: ${metrics.renderTime}ms`)
```

## Best Practices

1. **Image Optimization**: Use optimized images (WebP format recommended)
2. **Product Limit**: For best performance, limit to 20-50 products per carousel
3. **Responsive Images**: Provide multiple image sizes for different screen densities
4. **Preload Critical Images**: Consider preloading the first few product images
5. **Monitor Performance**: Use the performance hooks in development to optimize

## Accessibility

- Keyboard navigation support
- ARIA labels and roles
- Screen reader friendly
- Focus management
- High contrast support
