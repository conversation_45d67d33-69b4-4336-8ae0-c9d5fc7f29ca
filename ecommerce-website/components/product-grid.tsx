"use client"

import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useCart } from "@/components/cart-context"
import { ShoppingCart, Star } from "lucide-react"
import { Product } from "@/lib/types"
import { cn } from "@/lib/utils"
import { toast } from "sonner"

interface ProductGridProps {
  products: Product[]
  title?: string
  subtitle?: string
  className?: string
  maxProducts?: number
}

export function ProductGrid({
  products,
  title = "Featured Products",
  subtitle = "Discover our handpicked selection of premium products",
  className = "",
  maxProducts
}: ProductGridProps) {
  const { addItem } = useCart()

  // Limit products if maxProducts is specified
  const displayProducts = maxProducts ? products.slice(0, maxProducts) : products

  const handleAddToCart = (product: Product, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    addItem({
      id: product.id,
      name: product.name,
      price: product.salePrice || product.price,
      image: product.images[0] || "/placeholder.svg",
      quantity: 1
    })
    
    toast.success(`${product.name} added to cart!`)
  }

  if (displayProducts.length === 0) {
    return null
  }

  return (
    <section className={cn("py-16", className)}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            {title}
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            {subtitle}
          </p>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {displayProducts.map((product) => (
            <Link key={product.id} href={`/products/${product.slug}`} className="block">
              <Card className="group cursor-pointer hover:shadow-2xl hover:-translate-y-1 transition-all duration-300 border-slate-200 overflow-hidden bg-white rounded-xl shadow-md h-full">
                <CardContent className="p-0 flex flex-col h-full">
                  {/* Product Image */}
                  <div className="relative aspect-square overflow-hidden bg-slate-100">
                    <img
                      src={product.images[0] || "/placeholder.svg"}
                      alt={product.name}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    
                    {/* Sale Badge */}
                    {product.salePrice && (
                      <Badge className="absolute top-3 left-3 bg-red-500 hover:bg-red-600">
                        Sale
                      </Badge>
                    )}

                    {/* Quick Add to Cart */}
                    <div className="absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <Button
                        size="sm"
                        className="w-full bg-slate-900 hover:bg-slate-800 text-white"
                        onClick={(e) => handleAddToCart(product, e)}
                      >
                        <ShoppingCart className="h-4 w-4 mr-2" />
                        Quick Add
                      </Button>
                    </div>
                  </div>

                  {/* Product Info */}
                  <div className="p-6 flex-grow flex flex-col justify-between">
                    <div className="space-y-3">
                      {/* Product Name */}
                      <h3 className="font-semibold text-lg text-slate-900 line-clamp-2 group-hover:text-slate-700 transition-colors">
                        {product.name}
                      </h3>
                      
                      {/* Rating */}
                      <div className="flex items-center gap-1">
                        <div className="flex items-center">
                          {Array.from({ length: 5 }).map((_, i) => (
                            <Star
                              key={i}
                              className={cn(
                                "h-4 w-4",
                                i < Math.floor(product.rating)
                                  ? "fill-yellow-400 text-yellow-400"
                                  : "text-slate-300"
                              )}
                            />
                          ))}
                        </div>
                        <span className="text-sm text-slate-600 ml-1">
                          ({product.rating})
                        </span>
                      </div>
                    </div>

                    {/* Bottom Content */}
                    <div className="space-y-4 mt-4">
                      {/* Price */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-xl font-bold text-slate-900">
                            ${product.salePrice || product.price}
                          </span>
                          {product.salePrice && (
                            <span className="text-sm text-slate-500 line-through">
                              ${product.price}
                            </span>
                          )}
                        </div>
                        {product.salePrice && (
                          <Badge variant="destructive" className="text-xs">
                            {Math.round(((product.price - product.salePrice) / product.price) * 100)}% OFF
                          </Badge>
                        )}
                      </div>
                      
                      {/* Call to Action Button */}
                      <Button
                        className="w-full bg-slate-900 hover:bg-slate-800 text-white font-medium py-2.5 transition-all duration-200 hover:shadow-lg"
                        onClick={(e) => handleAddToCart(product, e)}
                      >
                        <ShoppingCart className="h-4 w-4 mr-2" />
                        Add to Cart
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </section>
  )
}
