import type { Product, Category, User, Review } from "./types"

export const mockCategories: Category[] = [
  {
    id: "1",
    name: "Electronics",
    slug: "electronics",
    description: "Latest gadgets and electronic devices",
    image: "/electronics-category.png",
  },
  {
    id: "2",
    name: "Fashion",
    slug: "fashion",
    description: "Trendy clothing and accessories",
    image: "/fashion-category.png",
  },
  {
    id: "3",
    name: "Home & Garden",
    slug: "home-garden",
    description: "Everything for your home and garden",
    image: "/home-garden-category.png",
  },
  {
    id: "4",
    name: "Beauty",
    slug: "beauty",
    description: "Heat & humidity friendly beauty products",
    image: "/health-beauty-category.jpg",
  },
  {
    id: "5",
    name: "Education",
    slug: "education",
    description: "KCPE & CBC aligned educational materials",
    image: "/books-media-category.jpg",
  },
  {
    id: "6",
    name: "Technology",
    slug: "technology",
    description: "Digital goods and tech solutions",
    image: "/sports-fitness-category.jpg",
  },
  {
    id: "7",
    name: "Furniture",
    slug: "furniture",
    description: "Rental-friendly, flat-pack furniture",
    image: "/images/furniture/sofas/spacejoy-RqO6kwm4tZY-unsplash.jpg",
  },
  {
    id: "8",
    name: "Food & Beverages",
    slug: "food-beverages",
    description: "Fresh food and premium beverages",
    image: "/images/food/burger/joseph-gonzalez-zcUgjyqEwe8-unsplash.jpg",
  },
]

export const mockProducts: Product[] = [
  // ORIGINAL PRODUCTS WITH REAL IMAGES
  {
    id: "1",
    name: "Wireless Headphones Pro",
    slug: "wireless-headphones-pro",
    description: "Premium wireless headphones with noise cancellation and superior sound quality.",
    price: 299.99,
    salePrice: 249.99,
    compareAtPrice: 399.99,
    rating: 4.8,
    images: ["/black-wireless-headphones.png", "/wireless-headphones-side.png", "/wireless-headphones-case.png"],
    model3d: "/models/headphones.glb",
    categoryId: "1",
    category: mockCategories[0],
    inventory: 50,
    sku: "WHP-001",
    weight: 0.3,
    dimensions: { length: 20, width: 18, height: 8 },
    tags: ["wireless", "audio", "premium", "noise-cancelling"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "3",
    name: "Designer Leather Jacket",
    slug: "designer-leather-jacket",
    description: "Premium leather jacket with modern cut and exceptional craftsmanship.",
    price: 599.99,
    salePrice: 479.99,
    compareAtPrice: 799.99,
    rating: 4.9,
    images: ["/leather-jacket-detail.png"],
    categoryId: "2",
    category: mockCategories[1],
    inventory: 15,
    sku: "LJ-001",
    weight: 1.2,
    dimensions: { length: 60, width: 50, height: 5 },
    tags: ["leather", "jacket", "fashion", "premium"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-12"),
    updatedAt: new Date("2024-01-12"),
  },
  {
    id: "4",
    name: "Modern Coffee Table",
    slug: "modern-coffee-table",
    description: "Sleek modern coffee table with glass top and wooden base.",
    price: 399.99,
    rating: 4.3,
    images: ["/modern-coffee-table-glass.jpg", "/coffee-table-wooden-base.jpg", "/coffee-table-living-room.jpg"],
    model3d: "/models/coffee-table.glb",
    categoryId: "3",
    category: mockCategories[2],
    inventory: 8,
    sku: "CT-001",
    weight: 25,
    dimensions: { length: 120, width: 60, height: 45 },
    tags: ["furniture", "table", "modern", "glass"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-01-08"),
    updatedAt: new Date("2024-01-08"),
  },
  // NEW PRODUCTS WITH REAL IMAGES
  // ELECTRONICS
  {
    id: "35",
    name: "Smart Refrigerator with Touch Display",
    slug: "smart-refrigerator-touch-display",
    description: "Modern smart refrigerator with touch display, energy efficient and spacious design perfect for modern kitchens.",
    price: 89999,
    salePrice: 79999,
    rating: 4.7,
    images: ["/images/electronics/refridgeretaors/fridge.jpg", "/images/electronics/refridgeretaors/lisa-anna-ZkWMfHPNWpw-unsplash.jpg"],
    categoryId: "1",
    category: mockCategories[0],
    inventory: 12,
    sku: "ELC-035",
    weight: 85,
    dimensions: { length: 60, width: 65, height: 180 },
    tags: ["refrigerator", "smart", "energy-efficient", "kitchen", "appliance"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-03-01"),
    updatedAt: new Date("2024-03-01"),
  },
  {
    id: "36",
    name: "55-inch 4K Smart TV",
    slug: "55-inch-4k-smart-tv",
    description: "Ultra HD 4K Smart TV with HDR support, built-in streaming apps, and crystal clear picture quality.",
    price: 45999,
    salePrice: 39999,
    rating: 4.8,
    images: ["/images/electronics/tv sets/chauhan-moniz-3A0bs74T8zc-unsplash.jpg", "/images/electronics/tv sets/daniel-korpai-8GDCzWrcE3M-unsplash.jpg"],
    categoryId: "1",
    category: mockCategories[0],
    inventory: 25,
    sku: "ELC-036",
    weight: 18,
    dimensions: { length: 123, width: 71, height: 8 },
    tags: ["tv", "4k", "smart-tv", "hdr", "streaming", "ultra-hd"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-03-02"),
    updatedAt: new Date("2024-03-02"),
  },
  // FASHION
  {
    id: "39",
    name: "Premium Air Force Sneakers",
    slug: "premium-air-force-sneakers",
    description: "Classic white Air Force style sneakers with premium leather construction and comfortable sole.",
    price: 8999,
    salePrice: 7499,
    rating: 4.7,
    images: ["/images/fashion/shoes/airforce.jpg", "/images/fashion/shoes/shoes.jpg"],
    categoryId: "2",
    category: mockCategories[1],
    inventory: 56,
    sku: "FSH-039",
    weight: 0.8,
    dimensions: { length: 30, width: 12, height: 10 },
    tags: ["sneakers", "air-force", "leather", "white", "classic", "comfortable"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-03-05"),
    updatedAt: new Date("2024-03-05"),
  },
  {
    id: "40",
    name: "Designer Denim Jacket",
    slug: "designer-denim-jacket",
    description: "Stylish denim jacket with modern cut and premium fabric. Perfect for casual and semi-formal occasions.",
    price: 6999,
    salePrice: 5499,
    rating: 4.6,
    images: ["/designer-denim-jacket.jpg", "/denim-jacket-optimized.svg", "/images/fashion/jackets/ana-nichita-IfNYCBwtAL4-unsplash.jpg", "/images/fashion/jackets/hannah-nicollet-jsF72-jeu9M-unsplash.jpg"],
    categoryId: "2",
    category: mockCategories[1],
    inventory: 34,
    sku: "FSH-040",
    weight: 0.7,
    dimensions: { length: 65, width: 55, height: 3 },
    tags: ["denim", "jacket", "designer", "casual", "modern", "premium"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-03-06"),
    updatedAt: new Date("2024-03-06"),
  },
  {
    id: "41",
    name: "Elegant Summer Dress",
    slug: "elegant-summer-dress",
    description: "Beautiful flowy summer dress perfect for warm weather. Lightweight fabric with elegant design.",
    price: 4999,
    salePrice: 3999,
    rating: 4.8,
    images: ["/images/fashion/dresses/samantha-gades-JeEemtLSdjU-unsplash.jpg", "/images/fashion/dresses/valerie-elash-gsKdPcIyeGg-unsplash.jpg"],
    categoryId: "2",
    category: mockCategories[1],
    inventory: 42,
    sku: "FSH-041",
    weight: 0.3,
    dimensions: { length: 120, width: 60, height: 2 },
    tags: ["dress", "summer", "elegant", "flowy", "lightweight", "warm-weather"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-03-07"),
    updatedAt: new Date("2024-03-07"),
  },
  // BEAUTY
  {
    id: "46",
    name: "Elegant Gold Jewelry Set",
    slug: "elegant-gold-jewelry-set",
    description: "Beautiful gold jewelry set including necklace and earrings. Perfect for special occasions and everyday elegance.",
    price: 15999,
    salePrice: 12999,
    rating: 4.9,
    images: ["/images/beauty/jewellery/segal-jewelry-NsH-CvU0deg-unsplash.jpg", "/images/beauty/jewellery/alireza-zarafshani-7CnWk58BsFk-unsplash.jpg"],
    categoryId: "4",
    category: mockCategories[3],
    inventory: 23,
    sku: "BTY-046",
    weight: 0.1,
    dimensions: { length: 25, width: 20, height: 3 },
    tags: ["jewelry", "gold", "elegant", "necklace", "earrings", "special-occasions"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-03-12"),
    updatedAt: new Date("2024-03-12"),
  },
  // FURNITURE
  {
    id: "48",
    name: "Modern Living Room Sofa",
    slug: "modern-living-room-sofa",
    description: "Comfortable modern sofa with premium upholstery. Perfect centerpiece for contemporary living rooms.",
    price: 45999,
    salePrice: 39999,
    rating: 4.7,
    images: ["/images/furniture/sofas/spacejoy-RqO6kwm4tZY-unsplash.jpg", "/images/furniture/sofas/nathan-fertig-FBXuXp57eM0-unsplash.jpg"],
    categoryId: "7",
    category: mockCategories[6],
    inventory: 15,
    sku: "FUR-048",
    weight: 45,
    dimensions: { length: 200, width: 90, height: 85 },
    tags: ["sofa", "modern", "living-room", "comfortable", "premium", "upholstery"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-03-14"),
    updatedAt: new Date("2024-03-14"),
  },
  {
    id: "50",
    name: "Ergonomic Office Chair",
    slug: "ergonomic-office-chair",
    description: "Professional ergonomic office chair with lumbar support and adjustable height. Perfect for long work sessions.",
    price: 15999,
    salePrice: 12999,
    rating: 4.6,
    images: ["/images/furniture/chairs/toa-heftiba-FV3GConVSss-unsplash.jpg", "/images/furniture/chairs/yevhen-ptashnyk-TGhaRbUwbxI-unsplash.jpg"],
    categoryId: "7",
    category: mockCategories[6],
    inventory: 25,
    sku: "FUR-050",
    weight: 18,
    dimensions: { length: 65, width: 65, height: 120 },
    tags: ["office-chair", "ergonomic", "lumbar-support", "adjustable", "professional", "work"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-03-16"),
    updatedAt: new Date("2024-03-16"),
  },
  // FOOD & BEVERAGES
  {
    id: "51",
    name: "Gourmet Pizza",
    slug: "gourmet-pizza",
    description: "Artisan pizza with premium toppings and fresh ingredients. Perfect for family dinners and gatherings.",
    price: 1999,
    salePrice: 1699,
    rating: 4.8,
    images: ["/images/food/pizza/chad-montano-MqT0asuoIcU-unsplash.jpg"],
    categoryId: "8",
    category: mockCategories[7],
    inventory: 50,
    sku: "FOD-051",
    weight: 0.8,
    dimensions: { length: 35, width: 35, height: 3 },
    tags: ["pizza", "gourmet", "artisan", "premium-toppings", "fresh-ingredients", "family"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-03-17"),
    updatedAt: new Date("2024-03-17"),
  },
  {
    id: "52",
    name: "Premium Burger Combo",
    slug: "premium-burger-combo",
    description: "Delicious gourmet burger with fresh ingredients and crispy fries. Perfect for a satisfying meal.",
    price: 1599,
    salePrice: 1299,
    rating: 4.9,
    images: ["/images/food/burger/jonathan-borba-8l8Yl2ruUsg-unsplash.jpg", "/images/food/burger/joseph-gonzalez-zcUgjyqEwe8-unsplash.jpg"],
    categoryId: "8",
    category: mockCategories[7],
    inventory: 75,
    sku: "FOD-052",
    weight: 0.6,
    dimensions: { length: 15, width: 15, height: 8 },
    tags: ["burger", "gourmet", "fresh-ingredients", "fries", "combo", "satisfying"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-03-18"),
    updatedAt: new Date("2024-03-18"),
  },
  {
    id: "53",
    name: "Crispy Potato Chips",
    slug: "crispy-potato-chips",
    description: "Premium crispy potato chips with natural flavors. Perfect snack for any time of the day.",
    price: 599,
    salePrice: 499,
    rating: 4.7,
    images: ["/images/food/chips/ashley-green-UbTUTDRqj-o-unsplash.jpg", "/images/food/chips/logan-weaver-lgnwvr-qgZRZI-pKgM-unsplash.jpg"],
    categoryId: "8",
    category: mockCategories[7],
    inventory: 200,
    sku: "FOD-053",
    weight: 0.15,
    dimensions: { length: 20, width: 15, height: 5 },
    tags: ["chips", "crispy", "potato", "natural-flavors", "snack", "anytime"],
    featured: true,
    status: "active",
    createdAt: new Date("2024-03-19"),
    updatedAt: new Date("2024-03-19"),
  },
]

export const mockUsers: User[] = [
  {
    id: "1",
    email: "<EMAIL>",
    name: "Admin User",
    role: "admin",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "2",
    email: "<EMAIL>",
    name: "John Doe",
    role: "customer",
    createdAt: new Date("2024-01-05"),
    updatedAt: new Date("2024-01-05"),
  },
]

export const mockReviews: Review[] = [
  {
    id: "1",
    productId: "1",
    userId: "2",
    user: mockUsers[1],
    rating: 5,
    title: "Amazing sound quality!",
    content: "These headphones exceeded my expectations. The noise cancellation is incredible.",
    verified: true,
    createdAt: new Date("2024-01-20"),
  },
  {
    id: "2",
    productId: "35",
    userId: "2",
    user: mockUsers[1],
    rating: 4,
    title: "Great refrigerator",
    content: "Love the smart features and energy efficiency. Very spacious and modern design.",
    verified: true,
    createdAt: new Date("2024-01-18"),
  },
]
