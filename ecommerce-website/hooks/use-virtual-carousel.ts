"use client"

import { useState, useEffect, useMemo } from "react"

interface UseVirtualCarouselProps {
  totalItems: number
  itemsPerView: number
  bufferSize?: number
}

export function useVirtualCarousel({
  totalItems,
  itemsPerView,
  bufferSize = 2
}: UseVirtualCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  
  // Calculate visible range with buffer for smooth scrolling
  const visibleRange = useMemo(() => {
    const start = Math.max(0, currentIndex - bufferSize)
    const end = Math.min(totalItems, currentIndex + itemsPerView + bufferSize)
    
    return { start, end }
  }, [currentIndex, itemsPerView, bufferSize, totalItems])
  
  // Calculate total slides needed
  const totalSlides = useMemo(() => {
    return Math.ceil(totalItems / itemsPerView)
  }, [totalItems, itemsPerView])
  
  // Get visible indices for rendering
  const visibleIndices = useMemo(() => {
    const indices = []
    for (let i = visibleRange.start; i < visibleRange.end; i++) {
      indices.push(i)
    }
    return indices
  }, [visibleRange])
  
  const goToSlide = (slideIndex: number) => {
    const newIndex = slideIndex * itemsPerView
    setCurrentIndex(Math.min(newIndex, totalItems - itemsPerView))
  }
  
  const nextSlide = () => {
    const nextIndex = currentIndex + itemsPerView
    if (nextIndex < totalItems) {
      setCurrentIndex(nextIndex)
    } else {
      // Loop to beginning
      setCurrentIndex(0)
    }
  }
  
  const prevSlide = () => {
    const prevIndex = currentIndex - itemsPerView
    if (prevIndex >= 0) {
      setCurrentIndex(prevIndex)
    } else {
      // Loop to end
      const lastSlideIndex = Math.floor((totalItems - 1) / itemsPerView) * itemsPerView
      setCurrentIndex(lastSlideIndex)
    }
  }
  
  const currentSlide = Math.floor(currentIndex / itemsPerView)
  
  return {
    currentIndex,
    currentSlide,
    totalSlides,
    visibleIndices,
    visibleRange,
    goToSlide,
    nextSlide,
    prevSlide,
    canGoNext: currentIndex + itemsPerView < totalItems,
    canGoPrev: currentIndex > 0
  }
}
