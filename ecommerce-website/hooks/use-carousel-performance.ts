"use client"

import { useEffect, useRef, useState } from "react"

interface PerformanceMetrics {
  fps: number
  renderTime: number
  memoryUsage?: number
}

export function useCarouselPerformance(enabled: boolean = false) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 0,
    renderTime: 0
  })
  
  const frameCount = useRef(0)
  const lastTime = useRef(performance.now())
  const renderStartTime = useRef(0)
  const animationFrameId = useRef<number>()

  useEffect(() => {
    if (!enabled) return

    const measurePerformance = () => {
      const now = performance.now()
      frameCount.current++

      // Calculate FPS every second
      if (now - lastTime.current >= 1000) {
        const fps = Math.round((frameCount.current * 1000) / (now - lastTime.current))
        
        setMetrics(prev => ({
          ...prev,
          fps,
          renderTime: now - renderStartTime.current
        }))

        frameCount.current = 0
        lastTime.current = now
      }

      animationFrameId.current = requestAnimationFrame(measurePerformance)
    }

    renderStartTime.current = performance.now()
    animationFrameId.current = requestAnimationFrame(measurePerformance)

    return () => {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current)
      }
    }
  }, [enabled])

  // Memory usage monitoring (if available)
  useEffect(() => {
    if (!enabled || !('memory' in performance)) return

    const measureMemory = () => {
      const memory = (performance as any).memory
      if (memory) {
        setMetrics(prev => ({
          ...prev,
          memoryUsage: Math.round(memory.usedJSHeapSize / 1024 / 1024) // MB
        }))
      }
    }

    const interval = setInterval(measureMemory, 2000)
    return () => clearInterval(interval)
  }, [enabled])

  return metrics
}

// Hook for optimizing carousel rendering
export function useCarouselOptimization() {
  const [shouldRender, setShouldRender] = useState(true)
  const lastRenderTime = useRef(performance.now())
  
  // Throttle rendering to maintain 60fps
  const throttleRender = () => {
    const now = performance.now()
    const timeSinceLastRender = now - lastRenderTime.current
    
    // Only render if enough time has passed (16.67ms for 60fps)
    if (timeSinceLastRender >= 16.67) {
      setShouldRender(true)
      lastRenderTime.current = now
    } else {
      setShouldRender(false)
    }
  }

  return {
    shouldRender,
    throttleRender
  }
}
