@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Optimized Product Carousel Styles */
@layer components {
  .optimized-carousel {
    /* Enable hardware acceleration for smooth animations */
    transform: translateZ(0);
    will-change: transform;
    /* Prevent layout shifts */
    contain: layout style paint;
    /* Smooth scrolling */
    scroll-behavior: smooth;
    /* Optimize for 60fps */
    animation-fill-mode: both;
  }

  .optimized-carousel-item {
    /* Optimize for smooth transitions */
    backface-visibility: hidden;
    transform: translateZ(0);
    /* Prevent repaints during animation */
    will-change: transform, opacity;
    /* Optimize compositing */
    isolation: isolate;
  }

  .optimized-carousel-image {
    /* Optimize image rendering */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    /* Prevent image flickering */
    backface-visibility: hidden;
    /* Smooth image scaling */
    transform: translateZ(0);
  }

  /* Smooth scroll behavior */
  .smooth-scroll {
    scroll-behavior: smooth;
  }

  /* Performance optimized hover effects */
  .hover-lift {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .hover-lift:hover {
    transform: translateY(-4px) translateZ(0);
  }

  /* Prevent layout shift during image loading */
  .aspect-square {
    aspect-ratio: 1 / 1;
  }

  /* Optimize text rendering */
  .optimized-text {
    text-rendering: optimizeSpeed;
    font-display: swap;
  }

  /* Reduce paint complexity */
  .reduce-paint {
    contain: layout style paint;
  }

  /* Prevent carousel glitches */
  .embla {
    overflow: hidden;
  }

  .embla__container {
    display: flex;
    touch-action: pan-y pinch-zoom;
    margin-left: calc(var(--slide-spacing, 1rem) * -1);
  }

  .embla__slide {
    transform: translate3d(0, 0, 0);
    flex: 0 0 var(--slide-size, 100%);
    min-width: 0;
    padding-left: var(--slide-spacing, 1rem);
  }

  /* Smooth transitions for all carousel elements */
  .embla__slide,
  .embla__container {
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  /* Prevent text selection during drag */
  .embla__container {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Slideshow Effects */
  .slideshow-enhanced {
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .slideshow-fade {
    transition: opacity 0.8s ease-in-out, transform 0.8s ease-in-out;
  }

  .slideshow-fade.slide-active {
    animation: fadeInSlide 0.8s ease-in-out;
  }

  .slideshow-zoom {
    transition: transform 0.6s ease-in-out, opacity 0.6s ease-in-out;
  }

  .slideshow-zoom.slide-active {
    animation: zoomInSlide 0.6s ease-in-out;
  }

  .slideshow-slide {
    transition: transform 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .slideshow-slide.slide-active {
    animation: slideInEffect 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  /* Slideshow Animations */
  @keyframes fadeInSlide {
    0% {
      opacity: 0;
      transform: translateY(20px) scale(0.95);
    }
    50% {
      opacity: 0.7;
      transform: translateY(10px) scale(0.98);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes zoomInSlide {
    0% {
      opacity: 0;
      transform: scale(0.8) rotate(-2deg);
    }
    50% {
      opacity: 0.8;
      transform: scale(0.95) rotate(-1deg);
    }
    100% {
      opacity: 1;
      transform: scale(1) rotate(0deg);
    }
  }

  @keyframes slideInEffect {
    0% {
      transform: translateX(-30px) rotateY(-10deg);
      opacity: 0;
    }
    50% {
      transform: translateX(-10px) rotateY(-3deg);
      opacity: 0.7;
    }
    100% {
      transform: translateX(0) rotateY(0deg);
      opacity: 1;
    }
  }

  /* Enhanced hover effects for slideshow */
  .slideshow-enhanced:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    .slideshow-fade,
    .slideshow-zoom,
    .slideshow-slide,
    .slideshow-enhanced {
      animation: none !important;
      transition: none !important;
    }

    .slideshow-enhanced:hover {
      transform: none !important;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .progress-bar {
      background: #000 !important;
    }

    .slideshow-enhanced {
      border: 2px solid #000 !important;
    }
  }

  /* Progress bar animation */
  .progress-bar {
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    animation: progressGlow 2s ease-in-out infinite alternate;
  }

  @keyframes progressGlow {
    0% {
      box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
    }
    100% {
      box-shadow: 0 0 20px rgba(139, 92, 246, 0.8);
    }
  }
}
