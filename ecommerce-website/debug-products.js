// Debug script to check which products might have image issues
const fs = require('fs');
const path = require('path');

// Read the mock data file
const mockDataPath = path.join(__dirname, 'lib', 'mock-data.ts');
const mockData = fs.readFileSync(mockDataPath, 'utf8');

// Extract product image paths
const imageRegex = /images:\s*\[(.*?)\]/gs;
const matches = [...mockData.matchAll(imageRegex)];

console.log('Checking product images...\n');

matches.forEach((match, index) => {
  const imagesString = match[1];
  const imagePaths = imagesString.split(',').map(path => 
    path.trim().replace(/['"]/g, '')
  );
  
  console.log(`Product ${index + 1}:`);
  imagePaths.forEach(imagePath => {
    const fullPath = path.join(__dirname, 'public', imagePath);
    const exists = fs.existsSync(fullPath);
    console.log(`  ${imagePath} - ${exists ? '✓ EXISTS' : '✗ MISSING'}`);
  });
  console.log('');
});
